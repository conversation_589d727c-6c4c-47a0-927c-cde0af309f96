@baseUrl = http://localhost:8000

### Health Check - Root endpoint
GET {{baseUrl}}/

### Health Check - Root endpoint with headers
GET {{baseUrl}}/
Accept: application/json
User-Agent: CodeOCR-Test-Client/1.0

### CodeOCR - Extract code from base64 image
POST {{baseUrl}}/api/codeocr
Content-Type: application/json

{
  "source": "data:image/png;base64,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"
}

### CodeOCR - Extract code from image URL
POST {{baseUrl}}/api/codeocr
Content-Type: application/json

{
  "source": "https://www.fengchao.pro/blog/assets/2024-07-10-pypika-function/index-image/image-20240711001853859.png"
}

### CodeOCR - Extract code from local file path
POST {{baseUrl}}/api/codeocr
Content-Type: application/json

{
  "source": "/Users/<USER>/Desktop/File/GitHub/codeocr/backend/test/code_screenshot.png"
}

### CodeOCR - Extract code from relative file path
POST {{baseUrl}}/api/codeocr
Content-Type: application/json

{
  "source": "backend/test/code_screenshot.png"
}

### CodeOCR - Test with empty source (should fail validation)
POST {{baseUrl}}/api/codeocr
Content-Type: application/json

{
  "source": ""
}

### CodeOCR - Test with missing source field (should fail validation)
POST {{baseUrl}}/api/codeocr
Content-Type: application/json

{
  "invalid_field": "test"
}

### CodeOCR - Test with null source (should fail validation)
POST {{baseUrl}}/api/codeocr
Content-Type: application/json

{
  "source": null
}

### CodeOCR - Test with invalid source type (should fail validation)
POST {{baseUrl}}/api/codeocr
Content-Type: application/json

{
  "source": 123
}

### CodeOCR - Test with array source (should fail validation)
POST {{baseUrl}}/api/codeocr
Content-Type: application/json

{
  "source": ["image1.png", "image2.png"]
}

### CodeOCR - Test with object source (should fail validation)
POST {{baseUrl}}/api/codeocr
Content-Type: application/json

{
  "source": {
    "url": "https://example.com/image.png"
  }
}

### API Documentation - OpenAPI JSON schema
GET {{baseUrl}}/openapi.json
Accept: application/json

### API Documentation - Swagger UI
GET {{baseUrl}}/docs
Accept: text/html

### API Documentation - ReDoc
GET {{baseUrl}}/redoc
Accept: text/html

### Test CORS preflight request
OPTIONS {{baseUrl}}/api/codeocr
Origin: http://localhost:3000
Access-Control-Request-Method: POST
Access-Control-Request-Headers: Content-Type
